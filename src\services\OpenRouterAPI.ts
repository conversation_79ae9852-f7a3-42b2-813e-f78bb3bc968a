import axios, { AxiosInstance } from 'axios'
import { emotionalAI } from './EmotionalAI'

export interface AIModel {
  id: string
  name: string
  description: string
  pricing: {
    prompt: string
    completion: string
  }
  context_length: number
  architecture: {
    modality: string
    tokenizer: string
    instruct_type?: string
  }
  top_provider: {
    max_completion_tokens?: number
    is_moderated: boolean
  }
  per_request_limits?: {
    prompt_tokens: string
    completion_tokens: string
  }
}

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
}

export class OpenRouterAPI {
  private apiClient: AxiosInstance
  private apiKey: string
  private baseURL = 'https://openrouter.ai/api/v1'

  // النماذج المجانية المدعومة
  private freeModels = [
    'deepseek/deepseek-prover-v2:free',
    'meta-llama/llama-3.3-8b-instruct:free',
    'qwen/qwen3-30b-a3b:free',
    'mistralai/mistral-7b-instruct:free',
    'google/gemini-2.0-flash-exp:free',
    'google/gemma-2-9b-it:free',
    // النماذج الأدبية العربية المتخصصة
    'literary/naguib-mahfouz:custom',
    'literary/sonallah-ibrahim:custom',
    'literary/ahlam-mosteghanemi:custom',
    'literary/ihsan-abdel-quddous:custom',
    'literary/radwa-ashour:custom',
    'literary/abdel-rahman-abnoudy:custom',
    'literary/ahmed-fouad-negm:custom'
  ]

  constructor(apiKey: string) {
    this.apiKey = apiKey
    this.apiClient = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://ai-chat-bot.local',
        'X-Title': 'AI Chat Bot'
      },
      timeout: 60000 // 60 ثانية
    })
  }

  // تحديث مفتاح API
  updateApiKey(newApiKey: string): void {
    this.apiKey = newApiKey
    this.apiClient.defaults.headers['Authorization'] = `Bearer ${newApiKey}`
  }

  // إعداد الرسالة النظامية حسب النموذج
  private getSystemMessage(model: string): string {
    // النماذج الأدبية العربية
    const literaryPrompts: Record<string, string> = {
      'literary/naguib-mahfouz:custom': `أنت نجيب محفوظ، عميد الأدب العربي ورائد الرواية العربية الحديثة. أنت معروف بأسلوبك الواقعي الاجتماعي، وقدرتك على رسم شخصيات عميقة ومعقدة، وتصوير الحياة في الأحياء الشعبية بالقاهرة. اكتب بأسلوب أدبي رفيع وعميق، واستخدم اللغة العربية الفصحى ببلاغة وجمال.`,

      'literary/sonallah-ibrahim:custom': `أنت صنع الله إبراهيم، الروائي المصري المتميز بأسلوبه التجريبي والنقدي. أنت معروف بتقنياتك السردية المبتكرة، واستخدام الوثائق والتقارير في النص، ونقدك اللاذع للأوضاع الاجتماعية والسياسية. اكتب بأسلوب مباشر وصريح، مع لمسات من السخرية والنقد الاجتماعي.`,

      'literary/ahlam-mosteghanemi:custom': `أنت أحلام مستغانمي، الروائية الجزائرية المشهورة بأسلوبها الرومانسي العميق. أنت معروفة بقدرتك على نسج قصص الحب مع الحنين للوطن والذاكرة، واستخدام لغة شاعرية جميلة مليئة بالعواطف. اكتبي بأسلوب عاطفي وحالم، مع تركيز على المشاعر الإنسانية والذاكرة.`,

      'literary/ihsan-abdel-quddous:custom': `أنت إحسان عبد القدوس، الروائي المصري المحبوب الذي أتقن فن الرومانسية الشعبية. أنت معروف بقدرتك على كتابة قصص حب مشوقة تلامس قلوب القراء، ومعالجة قضايا اجتماعية بأسلوب مباشر وممتع. اكتب بلغة بسيطة وواضحة، مع تركيز على الحبكة والتشويق.`,

      'literary/radwa-ashour:custom': `أنت رضوى عاشور، الروائية والناقدة المصرية المتميزة. أنت معروفة بقدرتك على نسج التاريخ مع الحاضر، واستكشاف قضايا الهوية والانتماء، والنظرة النسوية العميقة. اكتبي بأسلوب فكري عميق، مع تركيز على التاريخ والثقافة وقضايا المرأة.`,

      'literary/abdel-rahman-abnoudy:custom': `أنت عبد الرحمن الأبنودي، شاعر العامية المصرية وعاشق الريف والفلاحين. أنت معروف بقدرتك على التعبير عن هموم الناس البسطاء بلغتهم العامية الجميلة، ورسم صور شعرية رائعة للحياة الريفية. اكتب بالعامية المصرية الأصيلة، مع تركيز على البساطة والعمق والأصالة.`,

      'literary/ahmed-fouad-negm:custom': `أنت أحمد فؤاد نجم، شاعر العامية الثوري وصوت المقاومة والعدالة. أنت معروف بشعرك السياسي الجريء والمباشر، وقدرتك على استخدام العامية المصرية في نقد الظلم والفساد. اكتب بلغة عامية قوية ومباشرة، مع تركيز على قضايا العدالة الاجتماعية والمقاومة.`
    }

    // إذا كان النموذج أدبياً، استخدم الرسالة المخصصة
    if (literaryPrompts[model]) {
      return literaryPrompts[model]
    }

    // الرسالة الافتراضية للنماذج العادية
    return 'أنت مساعد ذكي مفيد. تجيب على الأسئلة باللغة العربية والإنجليزية حسب لغة السؤال. كن مفيداً ومهذباً ودقيقاً في إجاباتك.'
  }

  // إرسال رسالة للذكاء الاصطناعي مع التحليل العاطفي
  async sendMessage(
    message: string,
    model: string = 'meta-llama/llama-3.3-8b-instruct:free',
    conversationHistory: ChatMessage[] = []
  ): Promise<string> {
    try {
      // تحليل المشاعر في الرسالة
      const emotionalAnalysis = emotionalAI.getFullEmotionalAnalysis(message, model)

      // تحديد النموذج المناسب حسب الحالة العاطفية
      const selectedModel = emotionalAnalysis.shouldSwitchModel ?
        emotionalAnalysis.suggestedModel : model

      // إعداد الرسالة النظامية مع التحليل العاطفي
      const systemMessage = emotionalAnalysis.systemMessage

      const messages: ChatMessage[] = [
        {
          role: 'system',
          content: systemMessage
        },
        ...conversationHistory,
        {
          role: 'user',
          content: message
        }
      ]

      // إذا كان النموذج أدبياً مخصصاً، استخدم نموذج بديل
      const actualModel = selectedModel.includes(':custom') ? 'meta-llama/llama-3.3-8b-instruct:free' : selectedModel

      const response = await this.apiClient.post('/chat/completions', {
        model: actualModel,
        messages: messages,
        max_tokens: 2048,
        temperature: 0.8, // زيادة الإبداع للنماذج الأدبية
        top_p: 0.9,
        frequency_penalty: 0,
        presence_penalty: 0,
        stream: false
      })

      if (response.data?.choices?.[0]?.message?.content) {
        const aiResponse = response.data.choices[0].message.content.trim()

        // إضافة معلومات عن التحليل العاطفي (للتطوير)
        if (emotionalAnalysis.emotionalState.confidence > 0.7) {
          console.log('🧠 تحليل عاطفي:', {
            emotion: emotionalAnalysis.emotionalState.primary,
            intensity: emotionalAnalysis.emotionalState.intensity,
            suggestedModel: emotionalAnalysis.suggestedModel,
            modelSwitched: emotionalAnalysis.shouldSwitchModel
          })
        }

        return aiResponse
      } else {
        throw new Error('لم يتم الحصول على رد صحيح من الخدمة')
      }
    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error)

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw new Error('مفتاح API غير صحيح. يرجى التحقق من المفتاح في الإعدادات.')
        } else if (error.response?.status === 429) {
          throw new Error('تم تجاوز حد الطلبات. يرجى المحاولة لاحقاً.')
        } else if (error.response?.status === 500) {
          throw new Error('خطأ في الخادم. يرجى المحاولة لاحقاً.')
        } else if (error.code === 'ENOTFOUND') {
          throw new Error('لا يمكن الاتصال بالإنترنت. يرجى التحقق من الاتصال.')
        }
      }

      throw new Error(`خطأ في الاتصال: ${error.message}`)
    }
  }

  // الحصول على قائمة النماذج المتاحة
  async getAvailableModels(): Promise<AIModel[]> {
    try {
      const response = await this.apiClient.get('/models')

      if (response.data?.data) {
        // فلترة النماذج المجانية فقط
        const allModels: AIModel[] = response.data.data
        const freeModels = allModels.filter(model =>
          this.freeModels.includes(model.id) ||
          model.id.includes(':free') ||
          (model.pricing?.prompt === '0' && model.pricing?.completion === '0')
        )

        return freeModels
      }

      // إرجاع النماذج الافتراضية في حالة فشل الطلب
      return this.getDefaultModels()
    } catch (error) {
      console.error('خطأ في الحصول على النماذج:', error)
      // إرجاع النماذج الافتراضية في حالة الخطأ
      return this.getDefaultModels()
    }
  }

  // الحصول على النماذج الافتراضية
  private getDefaultModels(): AIModel[] {
    return [
      {
        id: 'meta-llama/llama-3.3-8b-instruct:free',
        name: 'Llama 3.3 8B Instruct (Free)',
        description: 'نموذج Meta Llama المتقدم للمحادثات',
        pricing: { prompt: '0', completion: '0' },
        context_length: 8192,
        architecture: { modality: 'text', tokenizer: 'llama' },
        top_provider: { is_moderated: false }
      },
      {
        id: 'deepseek/deepseek-prover-v2:free',
        name: 'DeepSeek Prover V2 (Free)',
        description: 'نموذج DeepSeek للبرهان والمنطق',
        pricing: { prompt: '0', completion: '0' },
        context_length: 4096,
        architecture: { modality: 'text', tokenizer: 'deepseek' },
        top_provider: { is_moderated: false }
      },
      {
        id: 'google/gemini-2.0-flash-exp:free',
        name: 'Gemini 2.0 Flash (Free)',
        description: 'نموذج Google Gemini السريع',
        pricing: { prompt: '0', completion: '0' },
        context_length: 8192,
        architecture: { modality: 'text', tokenizer: 'gemini' },
        top_provider: { is_moderated: true }
      },
      {
        id: 'mistralai/mistral-7b-instruct:free',
        name: 'Mistral 7B Instruct (Free)',
        description: 'نموذج Mistral للمحادثات',
        pricing: { prompt: '0', completion: '0' },
        context_length: 8192,
        architecture: { modality: 'text', tokenizer: 'mistral' },
        top_provider: { is_moderated: false }
      },
      {
        id: 'google/gemma-2-9b-it:free',
        name: 'Gemma 2 9B IT (Free)',
        description: 'نموذج Google Gemma للمحادثات',
        pricing: { prompt: '0', completion: '0' },
        context_length: 8192,
        architecture: { modality: 'text', tokenizer: 'gemma' },
        top_provider: { is_moderated: true }
      },
      // النماذج الأدبية العربية
      {
        id: 'literary/naguib-mahfouz:custom',
        name: '📚 نجيب محفوظ - الروائي',
        description: 'يحاكي أسلوب نجيب محفوظ في الواقعية الاجتماعية',
        pricing: { prompt: '0', completion: '0' },
        context_length: 4096,
        architecture: { modality: 'text', tokenizer: 'literary' },
        top_provider: { is_moderated: false }
      },
      {
        id: 'literary/sonallah-ibrahim:custom',
        name: '📚 صنع الله إبراهيم - الروائي',
        description: 'يحاكي أسلوب صنع الله إبراهيم التجريبي والنقدي',
        pricing: { prompt: '0', completion: '0' },
        context_length: 4096,
        architecture: { modality: 'text', tokenizer: 'literary' },
        top_provider: { is_moderated: false }
      },
      {
        id: 'literary/ahlam-mosteghanemi:custom',
        name: '📚 أحلام مستغانمي - الروائية',
        description: 'يحاكي أسلوب أحلام مستغانمي الرومانسي والحنين',
        pricing: { prompt: '0', completion: '0' },
        context_length: 4096,
        architecture: { modality: 'text', tokenizer: 'literary' },
        top_provider: { is_moderated: false }
      },
      {
        id: 'literary/ihsan-abdel-quddous:custom',
        name: '📚 إحسان عبد القدوس - الروائي',
        description: 'يحاكي أسلوب إحسان عبد القدوس في الرومانسية الشعبية',
        pricing: { prompt: '0', completion: '0' },
        context_length: 4096,
        architecture: { modality: 'text', tokenizer: 'literary' },
        top_provider: { is_moderated: false }
      },
      {
        id: 'literary/radwa-ashour:custom',
        name: '📚 رضوى عاشور - الروائية',
        description: 'يحاكي أسلوب رضوى عاشور في التاريخ والهوية',
        pricing: { prompt: '0', completion: '0' },
        context_length: 4096,
        architecture: { modality: 'text', tokenizer: 'literary' },
        top_provider: { is_moderated: false }
      },
      {
        id: 'literary/abdel-rahman-abnoudy:custom',
        name: '🎵 عبد الرحمن الأبنودي - الشاعر',
        description: 'يحاكي أسلوب عبد الرحمن الأبنودي في الشعر الشعبي',
        pricing: { prompt: '0', completion: '0' },
        context_length: 4096,
        architecture: { modality: 'text', tokenizer: 'literary' },
        top_provider: { is_moderated: false }
      },
      {
        id: 'literary/ahmed-fouad-negm:custom',
        name: '🎵 أحمد فؤاد نجم - الشاعر',
        description: 'يحاكي أسلوب أحمد فؤاد نجم في الشعر الثوري',
        pricing: { prompt: '0', completion: '0' },
        context_length: 4096,
        architecture: { modality: 'text', tokenizer: 'literary' },
        top_provider: { is_moderated: false }
      }
    ]
  }

  // اختبار الاتصال
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.apiClient.get('/models', { timeout: 10000 })
      return response.status === 200
    } catch (error) {
      console.error('فشل اختبار الاتصال:', error)
      return false
    }
  }

  // الحصول على معلومات الاستخدام
  async getUsageInfo(): Promise<any> {
    try {
      const response = await this.apiClient.get('/auth/key')
      return response.data
    } catch (error) {
      console.error('خطأ في الحصول على معلومات الاستخدام:', error)
      return null
    }
  }
}
