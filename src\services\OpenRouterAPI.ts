import axios, { AxiosInstance } from 'axios'

export interface AIModel {
  id: string
  name: string
  description: string
  pricing: {
    prompt: string
    completion: string
  }
  context_length: number
  architecture: {
    modality: string
    tokenizer: string
    instruct_type?: string
  }
  top_provider: {
    max_completion_tokens?: number
    is_moderated: boolean
  }
  per_request_limits?: {
    prompt_tokens: string
    completion_tokens: string
  }
}

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
}

export class OpenRouterAPI {
  private apiClient: AxiosInstance
  private apiKey: string
  private baseURL = 'https://openrouter.ai/api/v1'

  // النماذج المجانية المدعومة
  private freeModels = [
    'deepseek/deepseek-prover-v2:free',
    'meta-llama/llama-3.3-8b-instruct:free',
    'qwen/qwen3-30b-a3b:free',
    'mistralai/mistral-7b-instruct:free',
    'google/gemini-2.0-flash-exp:free',
    'google/gemma-2-9b-it:free'
  ]

  constructor(apiKey: string) {
    this.apiKey = apiKey
    this.apiClient = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://ai-chat-bot.local',
        'X-Title': 'AI Chat Bot'
      },
      timeout: 60000 // 60 ثانية
    })
  }

  // تحديث مفتاح API
  updateApiKey(newApiKey: string): void {
    this.apiKey = newApiKey
    this.apiClient.defaults.headers['Authorization'] = `Bearer ${newApiKey}`
  }

  // إرسال رسالة للذكاء الاصطناعي
  async sendMessage(
    message: string, 
    model: string = 'meta-llama/llama-3.3-8b-instruct:free',
    conversationHistory: ChatMessage[] = []
  ): Promise<string> {
    try {
      const messages: ChatMessage[] = [
        {
          role: 'system',
          content: 'أنت مساعد ذكي مفيد. تجيب على الأسئلة باللغة العربية والإنجليزية حسب لغة السؤال. كن مفيداً ومهذباً ودقيقاً في إجاباتك.'
        },
        ...conversationHistory,
        {
          role: 'user',
          content: message
        }
      ]

      const response = await this.apiClient.post('/chat/completions', {
        model: model,
        messages: messages,
        max_tokens: 2048,
        temperature: 0.7,
        top_p: 0.9,
        frequency_penalty: 0,
        presence_penalty: 0,
        stream: false
      })

      if (response.data?.choices?.[0]?.message?.content) {
        return response.data.choices[0].message.content.trim()
      } else {
        throw new Error('لم يتم الحصول على رد صحيح من الخدمة')
      }
    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error)
      
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw new Error('مفتاح API غير صحيح. يرجى التحقق من المفتاح في الإعدادات.')
        } else if (error.response?.status === 429) {
          throw new Error('تم تجاوز حد الطلبات. يرجى المحاولة لاحقاً.')
        } else if (error.response?.status === 500) {
          throw new Error('خطأ في الخادم. يرجى المحاولة لاحقاً.')
        } else if (error.code === 'ENOTFOUND') {
          throw new Error('لا يمكن الاتصال بالإنترنت. يرجى التحقق من الاتصال.')
        }
      }
      
      throw new Error(`خطأ في الاتصال: ${error.message}`)
    }
  }

  // الحصول على قائمة النماذج المتاحة
  async getAvailableModels(): Promise<AIModel[]> {
    try {
      const response = await this.apiClient.get('/models')
      
      if (response.data?.data) {
        // فلترة النماذج المجانية فقط
        const allModels: AIModel[] = response.data.data
        const freeModels = allModels.filter(model => 
          this.freeModels.includes(model.id) ||
          model.id.includes(':free') ||
          (model.pricing?.prompt === '0' && model.pricing?.completion === '0')
        )
        
        return freeModels
      }
      
      // إرجاع النماذج الافتراضية في حالة فشل الطلب
      return this.getDefaultModels()
    } catch (error) {
      console.error('خطأ في الحصول على النماذج:', error)
      // إرجاع النماذج الافتراضية في حالة الخطأ
      return this.getDefaultModels()
    }
  }

  // الحصول على النماذج الافتراضية
  private getDefaultModels(): AIModel[] {
    return [
      {
        id: 'meta-llama/llama-3.3-8b-instruct:free',
        name: 'Llama 3.3 8B Instruct (Free)',
        description: 'نموذج Meta Llama المتقدم للمحادثات',
        pricing: { prompt: '0', completion: '0' },
        context_length: 8192,
        architecture: { modality: 'text', tokenizer: 'llama' },
        top_provider: { is_moderated: false }
      },
      {
        id: 'deepseek/deepseek-prover-v2:free',
        name: 'DeepSeek Prover V2 (Free)',
        description: 'نموذج DeepSeek للبرهان والمنطق',
        pricing: { prompt: '0', completion: '0' },
        context_length: 4096,
        architecture: { modality: 'text', tokenizer: 'deepseek' },
        top_provider: { is_moderated: false }
      },
      {
        id: 'google/gemini-2.0-flash-exp:free',
        name: 'Gemini 2.0 Flash (Free)',
        description: 'نموذج Google Gemini السريع',
        pricing: { prompt: '0', completion: '0' },
        context_length: 8192,
        architecture: { modality: 'text', tokenizer: 'gemini' },
        top_provider: { is_moderated: true }
      },
      {
        id: 'mistralai/mistral-7b-instruct:free',
        name: 'Mistral 7B Instruct (Free)',
        description: 'نموذج Mistral للمحادثات',
        pricing: { prompt: '0', completion: '0' },
        context_length: 8192,
        architecture: { modality: 'text', tokenizer: 'mistral' },
        top_provider: { is_moderated: false }
      },
      {
        id: 'google/gemma-2-9b-it:free',
        name: 'Gemma 2 9B IT (Free)',
        description: 'نموذج Google Gemma للمحادثات',
        pricing: { prompt: '0', completion: '0' },
        context_length: 8192,
        architecture: { modality: 'text', tokenizer: 'gemma' },
        top_provider: { is_moderated: true }
      }
    ]
  }

  // اختبار الاتصال
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.apiClient.get('/models', { timeout: 10000 })
      return response.status === 200
    } catch (error) {
      console.error('فشل اختبار الاتصال:', error)
      return false
    }
  }

  // الحصول على معلومات الاستخدام
  async getUsageInfo(): Promise<any> {
    try {
      const response = await this.apiClient.get('/auth/key')
      return response.data
    } catch (error) {
      console.error('خطأ في الحصول على معلومات الاستخدام:', error)
      return null
    }
  }
}
