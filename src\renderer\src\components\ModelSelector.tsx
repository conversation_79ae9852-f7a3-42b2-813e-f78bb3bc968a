import React, { useState } from 'react'
import { ChevronDown, Cpu, Zap, Globe } from 'lucide-react'
import { AIModel } from '../types'
import './ModelSelector.css'

interface ModelSelectorProps {
  models: AIModel[]
  selectedModel: string
  onModelChange: (modelId: string) => void
}

const ModelSelector: React.FC<ModelSelectorProps> = ({
  models,
  selectedModel,
  onModelChange
}) => {
  const [isOpen, setIsOpen] = useState(false)

  const selectedModelData = models.find(m => m.id === selectedModel)

  const getModelIcon = (model: AIModel) => {
    // نماذج السينما والدراما
    if (model.id.includes('cinema/')) {
      if (model.id.includes('dialogue')) return '🗣️' // حوارات
      if (model.id.includes('screenplay')) return '📝' // سيناريو
      return '🎥' // سينما
    }

    // النماذج الأدبية
    if (model.id.includes('literary/')) {
      if (model.id.includes('abnoudy') || model.id.includes('negm')) return '🎵' // شعر
      return '📚' // رواية
    }

    // النماذج التقنية
    if (model.id.includes('llama')) return '🦙'
    if (model.id.includes('gemini')) return '💎'
    if (model.id.includes('mistral')) return '🌪️'
    if (model.id.includes('deepseek')) return '🔍'
    if (model.id.includes('gemma')) return '💠'
    return '🤖'
  }

  const getModelDescription = (model: AIModel) => {
    const contextLength = model.context_length ? `${model.context_length.toLocaleString()} رمز` : 'غير محدد'
    const isModerated = model.top_provider?.is_moderated ? 'مُراقب' : 'غير مُراقب'

    return `${contextLength} • ${isModerated}`
  }

  const handleModelSelect = (modelId: string) => {
    onModelChange(modelId)
    setIsOpen(false)
  }

  return (
    <div className="model-selector">
      <button
        className="model-selector-trigger"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
      >
        <div className="selected-model">
          <span className="model-icon">
            {selectedModelData ? getModelIcon(selectedModelData) : '🤖'}
          </span>
          <div className="model-info">
            <span className="model-name">
              {selectedModelData?.name || 'اختر نموذج'}
            </span>
            <span className="model-meta">
              {selectedModelData ? getModelDescription(selectedModelData) : 'لم يتم اختيار نموذج'}
            </span>
          </div>
        </div>
        <ChevronDown
          size={16}
          className={`chevron ${isOpen ? 'chevron-open' : ''}`}
        />
      </button>

      {isOpen && (
        <div className="model-dropdown">
          <div className="dropdown-header">
            <Cpu size={16} />
            <span>النماذج المتاحة</span>
          </div>

          <div className="models-list">
            {models.length === 0 ? (
              <div className="no-models">
                <span>لا توجد نماذج متاحة</span>
              </div>
            ) : (
              models.map((model) => (
                <button
                  key={model.id}
                  className={`model-option ${model.id === selectedModel ? 'selected' : ''}`}
                  onClick={() => handleModelSelect(model.id)}
                >
                  <div className="model-option-content">
                    <div className="model-option-header">
                      <span className="model-icon">
                        {getModelIcon(model)}
                      </span>
                      <span className="model-name">{model.name}</span>
                      {model.id.includes(':free') && (
                        <span className="free-badge">
                          <Zap size={12} />
                          مجاني
                        </span>
                      )}
                    </div>

                    <div className="model-description">
                      {model.description}
                    </div>

                    <div className="model-specs">
                      <div className="spec">
                        <Globe size={12} />
                        <span>{model.context_length?.toLocaleString() || 'غير محدد'} رمز</span>
                      </div>

                      <div className="spec">
                        <span className={`moderation-status ${model.top_provider?.is_moderated ? 'moderated' : 'unmoderated'}`}>
                          {model.top_provider?.is_moderated ? '🛡️ مُراقب' : '🔓 غير مُراقب'}
                        </span>
                      </div>
                    </div>
                  </div>
                </button>
              ))
            )}
          </div>

          <div className="dropdown-footer">
            <small>النماذج المجانية فقط • بواسطة OpenRouter</small>
          </div>
        </div>
      )}

      {/* طبقة الخلفية لإغلاق القائمة */}
      {isOpen && (
        <div
          className="model-selector-overlay"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  )
}

export default ModelSelector
